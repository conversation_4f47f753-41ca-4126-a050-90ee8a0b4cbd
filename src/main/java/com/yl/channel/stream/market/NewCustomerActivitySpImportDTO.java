package com.yl.channel.stream.market;

import com.yl.channel.dto.UpsUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/13 17:11
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerActivitySpImportDTO implements Serializable {
    // 其中的唯一id 用phone + customerCode保证
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 活动编码
     */
    private String marketActivityCode;
    /**
     * 日志ID
     */
    private Long logId;
    /**
     * 当前登陆用户
     */
    private UpsUser user;

}
